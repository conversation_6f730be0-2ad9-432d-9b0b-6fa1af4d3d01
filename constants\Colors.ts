// Define the color palette for the app
export const Colors = {
  // Base colors
  primary: '#FF4B00',
  secondary: '#FF4B00',
  background: '#ffffff',
  text: '#000000',
  lightGray: '#f2f2f7',
  mediumGray: '#c7c7cc',
  darkGray: '#8e8e93',
  border: '#e0e0e0',
  error: '#FF3B30',
  success: '#34C759',
  warning: '#FF9500',
  info: '#0A84FF',
  
  // Theme-specific colors
  light: {
    tint: '#007AFF',
    background: '#FFFFFF',
    card: '#FFFFFF',
    text: '#000000',
    border: '#E0E0E0',
    notification: '#FF3B30',
    tabBar: '#FFFFFF',
    tabBarInactive: '#8E8E93',
    tabBarActive: '#007AFF',
    inputBackground: '#F2F2F7',
    primary: '#FF4B00',
    textSecondary: '#8E8E93',
  },
  dark: {
    tint: '#0A84FF',
    background: '#000000',
    card: '#1C1C1E',
    text: '#FFFFFF',
    border: '#38383A',
    notification: '#FF453A',
    tabBar: '#1C1C1E',
    tabBarInactive: '#8E8E93',
    tabBarActive: '#0A84FF',
    inputBackground: '#1C1C1E',
    primary: '#FF4B00',
    textSecondary: '#AEAEB2',
  }
};

// Helper function to get the right color based on theme
export function getThemeColor(colorName: string, theme: 'light' | 'dark'): string {
  // Check if the color exists in the theme-specific colors
  if (colorName in Colors[theme]) {
    return Colors[theme][colorName as keyof typeof Colors.light] as string;
  }
  
  // Fallback to base colors if the color name exists there
  if (colorName in Colors) {
    const baseColor = Colors[colorName as keyof typeof Colors];
    if (typeof baseColor === 'string') {
      return baseColor;
    }
  }
  
  // Default fallback
  return Colors.text;
}


