import { LinearGradient } from 'expo-linear-gradient';
import * as Location from 'expo-location';
import { useRouter } from 'expo-router';
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  ActivityIndicator,
  Alert,
  Animated, // Added RefreshControl import
  ColorSchemeName // Added ColorSchemeName import for better type inference
  ,




















  Dimensions,
  Image,
  PanResponder,
  Platform,
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  TouchableOpacity,
  useColorScheme,
  View
} from 'react-native';

// Helper function to get full image URL from Supabase storage
const getImageUrl = (imagePath: string, bucket: 'images' | 'avatars' = 'images') => {
  if (!imagePath) return null;

  // If it's already a full URL (http/https), return as is
  if (imagePath.startsWith('http')) return imagePath;

  // If it's a local file path, return as is (for local images)
  if (imagePath.startsWith('file://')) return imagePath;

  // Construct Supabase storage URL for relative paths
  const supabaseUrl = 'https://upijhrlbwqliwyxqamfx.supabase.co';
  return `${supabaseUrl}/storage/v1/object/public/${bucket}/${imagePath}`;
};

// First, install the expo-ads-admob package
// Run: npx expo install expo-ads-admob

// Define the LocationType interface
interface LocationType {
  latitude: number;
  longitude: number;
}

// Fix the Animated.View type issues by importing the correct types
import { View as RNView } from 'react-native';
type AnimatedViewProps = Animated.AnimatedProps<RNView['props']>;

import MessageInputModal from '../../components/MessageInputModal';
import { Button } from '../../components/ui/Button';
import { IconSymbol } from '../../components/ui/IconSymbol';
import { ThemedText } from '../../components/ui/ThemedText';
import { Colors } from '../../constants/Colors';

// Import the real AuthContext
import { useAuth } from '../../context/AuthContext';
import { sendFriendRequest } from '../../services/friendNotification';

// Get screen dimensions
const { width, height } = Dimensions.get('window');

// Define responsive heights based on percentages
const HEADER_HEIGHT_PERCENT = 0.10; // 10% for header
const CARD_SECTION_HEIGHT_PERCENT = Platform.OS === 'android' ? 0.75 : 0.70; // Adjusted for Android to prevent overlap
const NAV_HEIGHT_PERCENT = Platform.OS === 'android' ? 0.15 : 0.10; // Adjusted for Android to ensure tab bar visibility
const GAP_PERCENT = 0.025; // 2.5% for gaps

// Calculate actual heights
const HEADER_HEIGHT = height * HEADER_HEIGHT_PERCENT;
const CARD_SECTION_HEIGHT = height * CARD_SECTION_HEIGHT_PERCENT;
const NAV_HEIGHT = height * NAV_HEIGHT_PERCENT;
const GAP_HEIGHT = height * GAP_PERCENT;

// Adjust card height to be responsive to different screen sizes
const CARD_HEIGHT = CARD_SECTION_HEIGHT; // Card height is the card section height

// Increase the gap to 10px on each side
const SIDE_GAP = Platform.OS === 'android' ? 5 : 10; // Further reduced side gap for wider cards on Android
// Adjust card width to account for the gaps
const CARD_WIDTH = width - (SIDE_GAP * 2);

// Import Supabase client
import { supabase } from '../../lib/supabase';

// Define the Profile interface to match your Supabase table
interface Profile {
  id: string;
  name: string;
  age?: number;
  bio?: string;
  photoURL?: string | null; // Allow null values
  images?: (string | null)[]; // Allow null values in images array
  distance?: number; // Add distance
  interests?: string[]; // Add interests
  location?: string; // Add location
  hobbies?: string[]; // Add hobbies
}

// Mock profiles for testing (these will be replaced by Supabase data)
const mockProfiles = [
  {
    name: 'Emma',
    age: 28,
    bio: 'Adventure seeker and coffee enthusiast',
    interests: ['Hiking', 'Photography', 'Travel'],
    id: 'mock-emma-1',
    images: ['https://notjustdev-dummy.s3.us-east-2.amazonaws.com/images/1.jpg']
  },
  {
    name: 'James',
    age: 32,
    bio: 'Foodie and fitness lover',
    interests: ['Cooking', 'Running', 'Reading'],
    id: 'mock-james-2',
    images: ['https://notjustdev-dummy.s3.us-east-2.amazonaws.com/images/2.jpg']
  },
  {
    name: 'Sophia',
    age: 26,
    bio: 'Art director with a passion for design',
    interests: ['Painting', 'Museums', 'Fashion'],
    id: 'mock-sophia-3',
    images: ['https://notjustdev-dummy.s3.us-east-2.amazonaws.com/images/3.jpg']
  },
  {
    name: 'Michael',
    age: 30,
    bio: 'Tech entrepreneur and dog lover',
    interests: ['Startups', 'Dogs', 'Hiking'],
    id: 'mock-michael-4',
    images: ['https://notjustdev-dummy.s3.us-east-2.amazonaws.com/images/4.jpg']
  },
  {
    name: 'Olivia',
    age: 27,
    bio: 'Yoga instructor and plant mom',
    interests: ['Yoga', 'Plants', 'Meditation'],
    id: 'mock-olivia-5',
    images: ['https://notjustdev-dummy.s3.us-east-2.amazonaws.com/images/5.jpg']
  }
];


// Define proper props interface for the DiscoveryCard component
interface DiscoveryCardProps {
  item: Profile;
  onSendFriendRequest: (userId: string) => void;
  onSaveProfile: (userId: string) => void;
  onViewDetails: (profile: Profile) => void;
  isFriendRequested: boolean;
  isSaved: boolean;
  // Updated type here:
  colorScheme: ColorSchemeName; // Can be 'light' | 'dark' | null | undefined
}

// Update getProfiles function to fetch from Supabase
const getProfiles = async (
  userId: string,
  gender?: string,
  preference?: string,
  limit?: number,
  lastVisible?: any
): Promise<{
  profiles: Profile[];
  lastVisible: any;
}> => {
  try {
    console.log('🔍 getProfiles called with:', { userId, gender, preference, limit, lastVisible });

    // Start building the query
    let query = supabase
      .from('profiles')
      .select('id, display_name, avatar_url, photos, bio, gender, preference, location, hobbies, created_at');

    // Only exclude current user if userId is provided and valid
    if (userId && userId !== 'currentUser') {
      query = query.neq('id', userId);
      console.log('🚫 Excluding user ID:', userId);
    } else {
      console.log('⚠️ No valid userId provided, showing all profiles');
    }

    query = query.order('created_at', { ascending: false });

    // Add filters if provided
    if (gender) {
      query = query.eq('gender', gender);
    }

    if (preference) {
      query = query.eq('preference', preference);
    }

    // Add pagination
    if (limit) {
      query = query.limit(limit);
    }

    if (lastVisible) {
      query = query.gt('created_at', lastVisible); // Use created_at for pagination instead of id
    }

    const { data, error } = await query;

    console.log('📊 Database query result:', {
      dataLength: data?.length || 0,
      error: error?.message,
      firstProfile: data?.[0]?.display_name
    });

    if (error && error.code === '42703' && error.message.includes('column "age" does not exist')) {
      console.warn('Age column not found, retrying without age column');
      let retryQuery = supabase
        .from('profiles')
        .select('id, display_name, avatar_url, photos, bio, gender, preference, location, hobbies, created_at')
        .neq('id', userId) // Don't show the user's own profile
        .order('created_at', { ascending: false });

      if (gender) {
        retryQuery = retryQuery.eq('gender', gender);
      }

      if (preference) {
        retryQuery = retryQuery.eq('preference', preference);
      }

      if (limit) {
        retryQuery = retryQuery.limit(limit);
      }

      if (lastVisible) {
        retryQuery = retryQuery.gt('created_at', lastVisible);
      }

      const { data: retryData, error: retryError } = await retryQuery;

      if (retryError) {
        console.error('Error fetching profiles after retry:', retryError);
        console.log('getProfiles (retry) returning:', { profiles: [], lastVisible: null });
        return { profiles: [], lastVisible: null };
      }

      // Fetch discover card images for all profiles in retry
      const profilesData: Profile[] = await Promise.all(retryData.map(async (profile: any) => {
        // Fetch discover card images for this profile
        const { data: imagesData, error: imagesError } = await supabase
          .from('discover_card_images')
          .select('image_url')
          .eq('user_id', profile.id)
          .order('created_at', { ascending: false });

        if (imagesError) {
          console.error(`Error fetching discover images for ${profile.display_name}:`, imagesError);
        }

        // Process images from discover_card_images table
        const discoverImages = imagesData?.map(img => getImageUrl(img.image_url, 'images')).filter(Boolean) || [];
        const avatarUrl = profile.avatar_url ? getImageUrl(profile.avatar_url, 'avatars') : null;

        const images = discoverImages.length > 0
          ? discoverImages
          : (avatarUrl ? [avatarUrl] : []);

        return {
          id: profile.id,
          name: profile.display_name,
          bio: profile.bio,
          photoURL: avatarUrl,
          images: images,
          gender: profile.gender,
          preference: profile.preference,
          location: profile.location,
          hobbies: profile.hobbies,
          age: profile.birthday_date ? new Date().getFullYear() - new Date(profile.birthday_date).getFullYear() : undefined,
        };
      }));

      console.log('getProfiles (retry) returning:', { profiles: profilesData || [], lastVisible: retryData && retryData.length > 0 ? retryData[retryData.length - 1].created_at : null });
      return {
        profiles: profilesData || [],
        lastVisible: retryData && retryData.length > 0 ? retryData[retryData.length - 1].created_at : null
      };
    }

    if (error) {
      console.error('Error fetching profiles:', error);
      console.log('getProfiles returning:', { profiles: [], lastVisible: null });
      return { profiles: [], lastVisible: null };
    }

    // Fetch discover card images for all profiles
    const profilesData: Profile[] = await Promise.all(data.map(async (profile: any) => {
      // Fetch discover card images for this profile
      const { data: imagesData, error: imagesError } = await supabase
        .from('discover_card_images')
        .select('image_url')
        .eq('user_id', profile.id)
        .order('created_at', { ascending: false });

      if (imagesError) {
        console.error(`Error fetching discover images for ${profile.display_name}:`, imagesError);
      }

      // Process images from discover_card_images table
      const discoverImages = imagesData?.map(img => getImageUrl(img.image_url, 'images')).filter(Boolean) || [];
      const avatarUrl = profile.avatar_url ? getImageUrl(profile.avatar_url, 'avatars') : null;

      const images = discoverImages.length > 0
        ? discoverImages
        : (avatarUrl ? [avatarUrl] : []);

      // Debug logging
      console.log(`Profile ${profile.display_name}:`, {
        discoverImages: imagesData?.map(img => img.image_url),
        avatar_url: profile.avatar_url,
        processedDiscoverImages: discoverImages,
        avatarUrl,
        finalImages: images
      });

      return {
        id: profile.id,
        name: profile.display_name,
        bio: profile.bio,
        photoURL: avatarUrl,
        images: images,
        gender: profile.gender,
        preference: profile.preference,
        location: profile.location,
        hobbies: profile.hobbies,
        age: profile.age, // Assign the age directly if it exists in the profile object
      };
    }));

    console.log('getProfiles returning:', { profiles: profilesData || [], lastVisible: data && data.length > 0 ? data[data.length - 1].created_at : null });
    return {
      profiles: profilesData || [],
      lastVisible: data && data.length > 0 ? data[data.length - 1].created_at : null
    };
  } catch (error) {
    console.error('Error in getProfiles:', error);
    console.log('getProfiles (catch) returning:', { profiles: [], lastVisible: null });
    return { profiles: [], lastVisible: null };
  }
};

// Define the component first
const DiscoveryCardComponent = ({
  item,
  onSendFriendRequest,
  onSaveProfile,
  onViewDetails,
  isFriendRequested,
  isSaved,
  colorScheme
}: DiscoveryCardProps) => {
  console.log('Rendering DiscoveryCardComponent for item:', item.id, item.name);
  console.log('Item images:', item.images);

  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const isIOS = Platform.OS === 'ios';

  const handleImageScroll = (event: any) => {
    const contentOffsetX = event.nativeEvent.contentOffset.x;
    const cardWidth = width - (SIDE_GAP * 2);
    const newIndex = Math.round(contentOffsetX / cardWidth);
    if (newIndex !== currentImageIndex) {
      setCurrentImageIndex(newIndex);
    }
  };

  return (
    <View
      style={[
        styles.card,
        { backgroundColor: Colors.dark.card } // Set to black for both dark and light
      ]}
    >
      {/* Upper section - Images with fully rounded corners */}
      <View style={styles.cardUpperSection}>
        <ScrollView
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          onScroll={handleImageScroll}
          scrollEventThrottle={16}
          style={styles.imageContainer}
        >
          {item.images && item.images.length > 0 ? (
            item.images.map((image, index) => {
              // Filter out null images and only render valid ones
              if (!image) return null;

              return (
                <Image
                  key={index}
                  source={{ uri: image }}
                  style={styles.image}
                  resizeMode="cover"
                  onError={(error) => {
                    console.log('Image load error for:', image, error.nativeEvent.error);
                  }}
                  onLoad={() => {
                    console.log('Image loaded successfully:', image);
                  }}
                />
              );
            })
          ) : (
            <View style={[styles.image, styles.noImagePlaceholder]}>
              <IconSymbol name="person" size={80} color="#666666" />
              <Text style={styles.noImageText}>No Photo</Text>
            </View>
          )}
        </ScrollView>

        {/* Image indicators */}
        <View style={styles.imageIndicators}>
          {item.images?.map((_, index) => (
            <View
              key={index}
              style={[
                styles.imageIndicator,
                {
                  width: index === currentImageIndex ? 24 : 8,
                  opacity: index === currentImageIndex ? 1 : 0.6
                }
              ]}
            />
          ))}
        </View>
      </View>

      {/* Lower section - Details with theme-appropriate background */}
      <View style={styles.cardLowerSection}>
        <LinearGradient
          colors={[Colors.dark.card, Colors.dark.background]}
          style={styles.lowerSectionGradient}
        >
          <View style={styles.cardContentContainer}>
            <View style={styles.cardMainContent}>
              <View style={styles.nameAgeContainer}>
                <Text style={[
                  styles.nameText,
                  // Use Colors.dark.primary, after defining it in Colors.ts
                  { color: Colors.dark.primary } // Use reddish orange color for name
                ]}>
                  {item.name}
                </Text>
              </View>
            </View>

            {/* Action buttons - now on the right side for both platforms */}
            <View style={styles.sideButtonsContainer}>
              <TouchableOpacity
                style={styles.sideActionButton}
                onPress={() => onSaveProfile(item.id)}
              >
                <IconSymbol
                  name={isSaved ? "bookmark.fill" : "bookmark"}
                  size={22}
                  color={isSaved ? Colors.secondary : Colors.dark.primary} // Use Colors.dark.primary
                />
              </TouchableOpacity>

              <TouchableOpacity
                style={styles.sideActionButton}
                onPress={() => onSendFriendRequest(item.id)}
              >
                <IconSymbol
                  name={isFriendRequested ? "checkmark" : "plus"}
                  size={22}
                  color={Colors.dark.primary} // Use Colors.dark.primary
                />
              </TouchableOpacity>
            </View>
          </View>
        </LinearGradient>
      </View>
    </View>
  );
};

// Then memoize it using React's memo
const DiscoveryCard = React.memo(DiscoveryCardComponent);

export default function DiscoveryScreen() {
  const colorScheme = useColorScheme();
  const router = useRouter();
  const { user } = useAuth();
  const [discoveries, setDiscoveries] = useState<Profile[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  console.log('DiscoveryScreen: currentIndex', currentIndex, 'discoveries.length', discoveries.length);
  const [lastVisible, setLastVisible] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [hasMoreData, setHasMoreData] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [sentRequests, setSentRequests] = useState<string[]>([]);
  const [savedProfiles, setSavedProfiles] = useState<string[]>([]);
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<string | null>('');
  const [selectedUserName, setSelectedUserName] = useState('');
  const [unreadCount, setUnreadCount] = useState(0);
  const [userPhotos, setUserPhotos] = useState<string[]>([]);
  const [notificationCount, setNotificationCount] = useState(0);
  const [friendRequestCount, setFriendRequestCount] = useState(0);
  const [userLocation, setUserLocation] = useState<LocationType | null>(null);
  const [totalFetched, setTotalFetched] = useState(0);
  const [isUserPremium, setIsUserPremium] = useState(false); // TODO: Get from user profile
  const userName = user?.displayName || 'User';

  // Create a new Animated.ValueXY for position tracking
  const position = useRef(new Animated.ValueXY()).current;

  // Get screen dimensions for swipe animations
  const { width, height } = Dimensions.get('window');

  // Function to get theme color - Removed as it's not directly used for theme colors anymore
  // const getThemeColor = (colorName: string): string => {
  //   const colors: Record<string, string> = {
  //     background: Colors.dark.background,
  //     card: Colors.dark.card,
  //     text: Colors.dark.text,
  //     tint: '#FF4B00'
  //   };
  //   return colors[colorName] || colors.text;
  // };

  // Function to fetch friend request count
  const fetchFriendRequestCount = useCallback(async () => {
    if (!user?.id) return;

    try {
      const { count, error } = await supabase
        .from('friend_requests')
        .select('*', { count: 'exact', head: true })
        .eq('receiver_id', user.id)
        .eq('status', 'pending');

      if (error) {
        console.error('Error fetching friend request count:', error);
        return;
      }

      setFriendRequestCount(count || 0);
    } catch (error) {
      console.error('Error in fetchFriendRequestCount:', error);
    }
  }, [user?.id]);

  // Function to check if user is premium
  const checkUserPremiumStatus = useCallback(async () => {
    if (!user?.id) return;

    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('is_premium')
        .eq('id', user.id)
        .single();

      if (error) {
        console.error('Error fetching user premium status:', error);
        return;
      }

      setIsUserPremium(data?.is_premium || false);
      console.log('User premium status:', data?.is_premium || false);
    } catch (error) {
      console.error('Error in checkUserPremiumStatus:', error);
    }
  }, [user?.id]);

  // Test function to force load real profiles
  const testLoadRealProfiles = useCallback(async () => {
    console.log('🧪 TEST: Force loading real profiles from database...');
    setLoading(true);
    try {
      const { profiles: testProfiles, lastVisible: testLastVisible } = await getProfiles(
        'test-user-id', // Use a test ID that won't match any real user
        undefined,
        undefined,
        4,
        null
      );
      console.log('🧪 TEST: Fetched', testProfiles.length, 'real profiles');
      if (testProfiles.length > 0) {
        setDiscoveries(testProfiles);
        setLastVisible(testLastVisible);
        setTotalFetched(testProfiles.length);
        console.log('🧪 TEST: Successfully loaded real profiles!');
      } else {
        console.log('🧪 TEST: No real profiles found, using mock profiles');
        setDiscoveries(mockProfiles);
        setTotalFetched(mockProfiles.length);
      }
    } catch (error) {
      console.error('🧪 TEST: Error loading real profiles:', error);
      setDiscoveries(mockProfiles);
      setTotalFetched(mockProfiles.length);
    } finally {
      setLoading(false);
    }
  }, []);

  const loadMoreProfiles = useCallback(async () => {
    if (loading || !user) return;

    // Check pagination limits based on user type
    const maxProfilesForNormal = 4;
    const profilesPerBatchPremium = 10;

    if (!isUserPremium && totalFetched >= maxProfilesForNormal) {
      console.log('Normal user has reached limit of', maxProfilesForNormal, 'profiles');
      setHasMoreData(false);
      return;
    }

    setLoading(true);
    console.log('loadMoreProfiles called. Current totalFetched:', totalFetched, 'isUserPremium:', isUserPremium);

    try {
      // Determine how many profiles to fetch
      let limit = isUserPremium ? profilesPerBatchPremium : maxProfilesForNormal - totalFetched;

      // For normal users, ensure we don't exceed the limit
      if (!isUserPremium && limit <= 0) {
        console.log('Normal user limit reached, no more profiles to fetch');
        setHasMoreData(false);
        setLoading(false);
        return;
      }

      const { profiles: newProfiles, lastVisible: newLastVisible } = await getProfiles(
        user?.id || 'currentUser', // Provide fallback if user.id is undefined
        undefined, // gender filter
        undefined, // preference filter
        limit,
        lastVisible
      );

      console.log('Fetched', newProfiles.length, 'new profiles');

      if (newProfiles.length > 0) {
        setDiscoveries(prev => {
          const updatedDiscoveries = [...prev, ...newProfiles];
          console.log('Discoveries updated. New length:', updatedDiscoveries.length);
          return updatedDiscoveries;
        });
        setLastVisible(newLastVisible);
        setTotalFetched(prev => prev + newProfiles.length);
        console.log('Total fetched updated to:', totalFetched + newProfiles.length);

        // Check if we should stop fetching more
        if (!isUserPremium && totalFetched + newProfiles.length >= maxProfilesForNormal) {
          console.log('Normal user reached maximum profiles limit');
          setHasMoreData(false);
        }
      } else {
        console.log('No new profiles fetched from database.');
        // If this is the first load and no profiles found, use mock profiles
        if (discoveries.length === 0 && totalFetched === 0) {
          console.log('First load with no profiles found, falling back to mock profiles');
          setDiscoveries(mockProfiles);
          setTotalFetched(mockProfiles.length);
        }
        setHasMoreData(false);
      }
    } catch (error) {
      console.error('Failed to load more profiles:', error);
      // If this is the first load and there's an error, use mock profiles
      if (discoveries.length === 0 && totalFetched === 0) {
        console.log('First load failed, falling back to mock profiles');
        setDiscoveries(mockProfiles);
        setTotalFetched(mockProfiles.length);
      }
    } finally {
      setLoading(false);
      console.log('loadMoreProfiles finished. Loading set to false.');
    }
  },
    [loading, user?.id, lastVisible, totalFetched, isUserPremium]);

  // Helper function to calculate distance between two locations
  const calculateDistance = (location1: LocationType | null, location2: LocationType | null): number => {
    if (!location1 || !location2) return 0;

    // Simple distance calculation (you might want to use a more accurate formula)
    const distance = Math.sqrt(
      Math.pow(location2.latitude - location1.latitude, 2) +
      Math.pow(location2.longitude - location1.longitude, 2)
    ) * 69; // Rough miles conversion

    return Math.round(distance);
  };

  // Function to handle refresh
  const handleRefresh = useCallback(async () => {
    if (loading) return; // Prevent multiple refreshes

    console.log('🔄 REFRESH TRIGGERED - resetting all data');
    console.trace('Refresh called from:'); // This will show the call stack
    setRefreshing(true);
    setLastVisible(null);
    setDiscoveries([]);
    setCurrentIndex(0);
    setTotalFetched(0);
    setHasMoreData(true);

    // Call loadMoreProfiles directly instead of depending on it
    if (user) {
      setLoading(true);
      try {
        const limit = isUserPremium ? 10 : 4;
        const { profiles: newProfiles, lastVisible: newLastVisible } = await getProfiles(
          user?.id || 'currentUser', // Provide fallback if user.id is undefined
          undefined,
          undefined,
          limit,
          null // Start fresh
        );

        if (newProfiles.length > 0) {
          setDiscoveries(newProfiles);
          setLastVisible(newLastVisible);
          setTotalFetched(newProfiles.length);
        } else {
          console.log('No profiles found on refresh. Setting discoveries to mock profiles.');
          setDiscoveries(mockProfiles); // Fallback to mock profiles if no data
          setHasMoreData(false); // No more data from DB
        }
      } catch (error) {
        console.error('Error during refresh:', error);
        console.log('Falling back to mock profiles due to refresh error.');
        setDiscoveries(mockProfiles); // Fallback to mock profiles on error
      } finally {
        setLoading(false);
        setRefreshing(false);
      }
    } else {
      console.log('User not logged in, falling back to mock profiles on refresh.');
      setDiscoveries(mockProfiles); // Fallback if no user
      setLoading(false);
      setRefreshing(false);
    }
  },
    [loading, user?.id, isUserPremium]);

  // Create PanResponder for handling swipe gestures
  const panResponder = useRef(
    PanResponder.create({
      onStartShouldSetPanResponder: (evt, gestureState) => {
        console.log('onStartShouldSetPanResponder called');
        return true;
      },
      onMoveShouldSetPanResponder: (evt, gestureState) => {
        console.log('onMoveShouldSetPanResponder called', gestureState.dx, gestureState.dy);
        return Math.abs(gestureState.dx) > 5 || Math.abs(gestureState.dy) > 5;
      },
      onPanResponderGrant: (evt, gestureState) => {
        console.log('PanResponder granted');
      },
      onPanResponderMove: (evt, gestureState) => {
        // Allow both horizontal and vertical movement
        position.setValue({ x: gestureState.dx, y: gestureState.dy });
        console.log('PanResponderMove:', gestureState.dx, gestureState.dy);
      },
      onPanResponderRelease: (evt, gestureState) => {
        console.log('PanResponderRelease:', gestureState.dx, gestureState.dy);
        const swipeThreshold = 100;

        // Check for horizontal swipes first (left/right)
        if (Math.abs(gestureState.dx) > swipeThreshold) {
          if (gestureState.dx > 0) {
            // Swipe right - Like/Accept
            console.log('Swiped right - Like');
            handleSwipeRight();
          } else {
            // Swipe left - Pass/Reject
            console.log('Swiped left - Pass');
            handleSwipeLeft();
          }
        }
        // Check for vertical swipes (up/down)
        else if (Math.abs(gestureState.dy) > swipeThreshold) {
          if (gestureState.dy < 0) {
            // Swipe up - Next profile
            console.log('Swiped up - Next profile');
            handleSwipeUp();
          } else {
            // Swipe down - Previous profile
            console.log('Swiped down - Previous profile');
            handleSwipeDown();
          }
        } else {
          // Reset position if swipe was not strong enough
          console.log('Swipe not strong enough, resetting position');
          resetPosition();
        }
      },
      onPanResponderTerminationRequest: () => false,
    })
  ).current;

  // Helper function to reset card position
  const resetPosition = useCallback(() => {
    console.log('resetPosition called');
    Animated.spring(position, {
      toValue: { x: 0, y: 0 },
      friction: 4,
      useNativeDriver: true
    }).start();
  },
    [position]);

  // Swipe handler functions
  const handleSwipeLeft = useCallback(() => {
    console.log('Handling swipe left - Pass');
    Animated.timing(position, {
      toValue: { x: -width - 100, y: 0 },
      duration: 300,
      useNativeDriver: true
    }).start(() => {
      goToNextCard();
    });
  }, [position]);

  const handleSwipeRight = useCallback(() => {
    console.log('Handling swipe right - Like');
    Animated.timing(position, {
      toValue: { x: width + 100, y: 0 },
      duration: 300,
      useNativeDriver: true
    }).start(() => {
      goToNextCard();
    });
  }, [position]);

  const handleSwipeUp = useCallback(() => {
    console.log('Handling swipe up - Next profile');
    Animated.timing(position, {
      toValue: { x: 0, y: -height - 100 },
      duration: 300,
      useNativeDriver: true
    }).start(() => {
      goToNextCard();
    });
  }, [position]);

  const handleSwipeDown = useCallback(() => {
    console.log('Handling swipe down - Previous profile');
    if (currentIndex > 0) {
      Animated.timing(position, {
        toValue: { x: 0, y: height + 100 },
        duration: 300,
        useNativeDriver: true
      }).start(() => {
        setCurrentIndex(currentIndex - 1);
        position.setValue({ x: 0, y: 0 });
      });
    } else {
      resetPosition();
    }
  }, [position, currentIndex]);

  // Function to go to next card
  const goToNextCard = useCallback(() => {
    console.log('🔄 goToNextCard called. currentIndex:', currentIndex, 'discoveries.length:', discoveries.length, 'hasMoreData:', hasMoreData, 'isUserPremium:', isUserPremium);

    if (currentIndex < discoveries.length - 1) {
      console.log('✅ Going to next card in current batch. New index will be:', currentIndex + 1);
      setCurrentIndex(currentIndex + 1);
      position.setValue({ x: 0, y: 0 });
    } else if (hasMoreData && isUserPremium) {
      console.log('🔄 Premium user - loading more profiles');
      loadMoreProfiles();
      position.setValue({ x: 0, y: 0 });
    } else {
      console.log('🔚 No more cards available - reached end for this user type');
      // For non-premium users or when no more data, just reset position
      // Don't automatically refresh, let user manually refresh if they want
      position.setValue({ x: 0, y: 0 });
    }
  }, [currentIndex, discoveries.length, hasMoreData, isUserPremium, position, loadMoreProfiles]);

  // Function to handle sending friend requests
  const handleSendFriendRequest = useCallback((id: string) => {
    try {
      // Check if we've already sent a request to this user
      const alreadyRequested = sentRequests.includes(id);

      if (alreadyRequested) {
        // Remove from sent requests (cancel request)
        setSentRequests(sentRequests.filter(reqId => reqId !== id));

        // Show feedback
        Alert.alert('Request Canceled', 'Friend request has been canceled.');
      } else {
        // Find the discovery item to get the name
        const discovery = discoveries.find((d: Profile) => d.id === id);
        const recipientName = discovery?.name || 'this user';

        // Show message modal
        setSelectedUserId(id);
        setSelectedUserName(recipientName);
        setShowMessageModal(true);
      }
    } catch (error) {
      console.error('Error sending friend request:', error);
      Alert.alert('Error', 'Failed to send friend request. Please try again.');
    }
  },
    [discoveries, sentRequests]);

  // Handle sending the friend request with a message
  const handleSendRequestWithMessage = useCallback(async (message: string) => {
    if (!selectedUserId) return;

    try {
      // Add to sent requests
      setSentRequests([...sentRequests, selectedUserId]);

      // Get the current user's info
      const currentUserId = user?.id || 'currentUser';
      const currentUserName = user?.displayName || userName;
      const currentUserPhoto = user?.photoURL;

      // Send a friend request notification with the message
      const success = await sendFriendRequest(
        currentUserId,
        currentUserName,
        currentUserPhoto || '', // Add fallback empty string for undefined
        selectedUserId,
        message.trim() || `Hi ${selectedUserName}, I'd like to connect with you!`
      );

      if (success) {
        // Show personalized feedback
        Alert.alert(
          'Request Sent',
          `Friend request has been sent to ${selectedUserName} successfully!`
        );
      } else {
        // Show error feedback
        Alert.alert(
          'Request Failed',
          'Failed to send friend request. You may have already sent a request to this user.'
        );
        // Remove from sent requests if there was an error
        setSentRequests(sentRequests.filter(id => id !== selectedUserId));
      }
    } catch (error) {
      console.error('Error sending friend request:', error);
      Alert.alert('Error', 'Failed to send friend request. Please try again.');
      // Remove from sent requests if there was an error
      setSentRequests(sentRequests.filter(id => id !== selectedUserId));
    } finally {
      // Reset modal state
      setShowMessageModal(false);
      setSelectedUserId('');
      setSelectedUserName('');
    }
  },
    [user, userName, sentRequests, selectedUserId, selectedUserName]);

  // Function to handle saving profiles
  const handleSaveProfile = useCallback((id: string) => {
    // Check if profile is already saved
    const alreadySaved = savedProfiles.includes(id);

    if (alreadySaved) {
      // Remove from saved profiles
      setSavedProfiles(savedProfiles.filter(savedId => savedId !== id)); // Fixed typo here
    } else {
      // Add to saved profiles
      setSavedProfiles([...savedProfiles, id]);
    }

    // Log the action
    console.log(alreadySaved ?
      `Removed user ${id} from saved profiles` :
      `Saved user ${id} to profiles`);
  },
    [savedProfiles]);

  // Function to handle viewing profile details
  const handleViewDetails = useCallback((profile: Profile) => {
    // Navigate to profile details page
    router.push({
      pathname: '/profile-details/[id]',
      params: { id: profile.id }
    });
  },
    [router]);

  useEffect(() => {
    console.log('🔍 Initial load useEffect triggered. Conditions:');
    console.log('  - user:', !!user);
    console.log('  - user.id:', user?.id);
    console.log('  - discoveries.length:', discoveries.length);
    console.log('  - loading:', loading);
    console.log('  - refreshing:', refreshing);

    if (user && discoveries.length === 0 && !loading && !refreshing) {
      console.log('✅ All conditions met - starting initial load...');
      checkUserPremiumStatus();
      fetchFriendRequestCount();
      loadMoreProfiles();
    } else {
      console.log('❌ Conditions not met for initial load');
    }
  }, [user?.id]); // Only depend on user.id to prevent infinite loops

  useEffect(() => {
    console.log('📊 Current discoveries length:', discoveries.length);
    console.log('📊 Current index:', currentIndex);
    console.log('📊 Total fetched:', totalFetched);
    console.log('📊 Has more data:', hasMoreData);
    console.log('📊 Is user premium:', isUserPremium);
    if (discoveries.length > 0) {
      console.log('📋 Discoveries:', discoveries.map(d => `${d.name} (${d.id})`));
    } else {
      console.log('❌ Discoveries array is empty!');
    }
  }, [discoveries, currentIndex, totalFetched, hasMoreData, isUserPremium]);

  useEffect(() => {
    console.log('Current index changed to:', currentIndex);
  },
    [currentIndex]);

  // Add the useEffect for getting location inside the component
  useEffect(() => {
    // Get user location
    const getUserLocation = async () => {
      try {
        let { status } = await Location.requestForegroundPermissionsAsync();
        if (status !== 'granted') {
          Alert.alert('Permission to access location was denied');
          return;
        }

        let location = await Location.getCurrentPositionAsync({});
        setUserLocation({
          latitude: location.coords.latitude,
          longitude: location.coords.longitude,
        });
        console.log('User location:', location.coords);
      } catch (error) {
        console.error('Error getting user location:', error);
      }
    };

    getUserLocation();
  }, []); // Empty dependency array means this runs once on mount


  // Determine the current card to display
  const currentCard = discoveries[currentIndex];

  // Animated style for the card
  const animatedCardStyle = {
    transform: position.getTranslateTransform(),
  };

  // Logic to determine if "no more profiles" message should show
  const showNoMoreProfiles = !loading && !refreshing && discoveries.length === 0;
  const showEndOfFeed = !loading && !refreshing && currentIndex >= discoveries.length && !hasMoreData;


  return (
    <SafeAreaView style={styles.safeArea}>
      <StatusBar barStyle={colorScheme === 'dark' ? 'light-content' : 'dark-content'} />

      {/* Header Section */}
      <View style={styles.header}>
        {/* Changed to children instead of type prop */}
        <ThemedText style={{ color: Colors.dark.text, fontSize: 28, fontWeight: 'bold' }}>Discovery</ThemedText>
        <View style={styles.headerRight}>
          <TouchableOpacity onPress={() => router.push('/notifications')} style={styles.headerButton}>
            <IconSymbol name="bell.fill" size={24} color={Colors.dark.tint} />
            {notificationCount > 0 && (
              <View style={styles.badge}>
                <Text style={styles.badgeText}>{notificationCount}</Text>
              </View>
            )}
          </TouchableOpacity>
          <TouchableOpacity onPress={() => router.push('/friend-requests')} style={styles.headerButton}>
            <IconSymbol name="person.crop.circle.badge.plus" size={24} color={Colors.dark.tint} />
            {friendRequestCount > 0 && (
              <View style={styles.badge}>
                <Text style={styles.badgeText}>{friendRequestCount}</Text>
              </View>
            )}
          </TouchableOpacity>
        </View>
      </View>
      {/* End Header Section */}

      {/* Card Section */}
      <View style={styles.cardSection}>
        {showNoMoreProfiles ? (
          <View style={styles.noProfilesContainer}>
            <IconSymbol name="magnifyingglass" size={60} color={Colors.dark.tint} />
            {/* Changed to children instead of type prop */}
            <ThemedText style={[styles.noProfilesText, { fontSize: 22, fontWeight: '600' }]}>
              No profiles found right now.
            </ThemedText>
            <ThemedText style={styles.noProfilesSubText}>
              Try adjusting your filters or check back later!
            </ThemedText>
            <Button
              title="Refresh Feed"
              onPress={handleRefresh}
              disabled={refreshing}
              style={{ marginTop: 20 }}
            />
            <Button
              title="🧪 Test Load Real Profiles"
              onPress={testLoadRealProfiles}
              disabled={loading}
              style={{ marginTop: 10 }}
            />
          </View>
        ) : currentCard ? (
          <Animated.View
            style={[styles.animatedCardWrapper, animatedCardStyle]}
            {...panResponder.panHandlers}
          >
            <DiscoveryCard
              item={currentCard}
              onSendFriendRequest={handleSendFriendRequest}
              onSaveProfile={handleSaveProfile}
              onViewDetails={handleViewDetails}
              isFriendRequested={sentRequests.includes(currentCard.id)}
              isSaved={savedProfiles.includes(currentCard.id)}
              colorScheme={colorScheme}
            />
          </Animated.View>
        ) : (
          <View style={styles.loadingContainer}>
            {loading ? (
              <>
                <ActivityIndicator size="large" color={Colors.dark.primary} />
                <Text style={styles.loadingText}>Loading more profiles...</Text>
              </>
            ) : (
              <View style={styles.noProfilesContainer}>
                <IconSymbol name="magnifyingglass" size={60} color={Colors.dark.tint} />
                {/* Changed to children instead of type prop */}
                <ThemedText style={[styles.noProfilesText, { fontSize: 22, fontWeight: '600' }]}>
                  No profiles found right now.
                </ThemedText>
                <ThemedText style={styles.noProfilesSubText}>
                  Try adjusting your filters or check back later!
                </ThemedText>
                <Button
                  title="Refresh Feed"
                  onPress={handleRefresh}
                  disabled={refreshing}
                  style={{ marginTop: 20 }}
                />
                <Button
                  title="🧪 Test Load Real Profiles"
                  onPress={testLoadRealProfiles}
                  disabled={loading}
                  style={{ marginTop: 10 }}
                />
              </View>
            )}
          </View>
        )}
      </View>
      {/* End Card Section */}

      {/* Navigation Section */}
      <View style={styles.navigationSection}>
        <TouchableOpacity style={styles.navButton} onPress={handleSwipeLeft}>
          <IconSymbol name="xmark.circle.fill" size={60} color="#FF0000" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.navButton} onPress={handleSwipeUp}>
          <IconSymbol name="arrow.up.circle.fill" size={60} color="#007AFF" />
        </TouchableOpacity>
        <TouchableOpacity style={styles.navButton} onPress={handleSwipeRight}>
          <IconSymbol name="heart.circle.fill" size={60} color="#00C853" />
        </TouchableOpacity>
      </View>
      {/* End Navigation Section */}

      <MessageInputModal
        visible={showMessageModal}
        title="Add a Message"
        message={`Add a personal message to send with your friend request to ${selectedUserName}:`}
        defaultValue={`Hi ${selectedUserName}, I'd like to connect with you!`}
        onCancel={() => {
          setShowMessageModal(false);
          setSelectedUserId(null);
          setSelectedUserName('');
        }}
        onSubmit={handleSendRequestWithMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  safeArea: {
    flex: 1,
    backgroundColor: Colors.dark.background,
    paddingTop: Platform.OS === 'android' ? StatusBar.currentHeight : 0,
  },
  header: {
    height: HEADER_HEIGHT,
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 20,
    backgroundColor: Colors.dark.background,
  },
  headerRight: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerButton: {
    marginLeft: 15,
    position: 'relative',
  },
  badge: {
    position: 'absolute',
    right: -8,
    top: -8,
    backgroundColor: Colors.dark.tint,
    borderRadius: 10,
    width: 20,
    height: 20,
    justifyContent: 'center',
    alignItems: 'center',
  },
  badgeText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: 'bold',
  },
  cardSection: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingVertical: GAP_HEIGHT,
    paddingHorizontal: SIDE_GAP,
  },
  animatedCardWrapper: {
    width: CARD_WIDTH,
    height: CARD_HEIGHT,
    position: 'absolute', // Important for stacking cards
  },
  card: {
    width: CARD_WIDTH,
    height: CARD_HEIGHT,
    borderRadius: 20,
    overflow: 'hidden', // Ensures rounded corners clip content
    elevation: 5,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  cardUpperSection: {
    flex: 0.7, // Take 70% of the card height for images
    position: 'relative',
  },
  imageContainer: {
    width: '100%',
    height: '100%',
  },
  image: {
    width: CARD_WIDTH, // Image width should match card width
    height: '100%',
    borderRadius: 20, // Apply border radius here for images
  },
  noImagePlaceholder: {
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#333333',
  },
  noImageText: {
    color: '#666666',
    marginTop: 10,
    fontSize: 16,
  },
  imageIndicators: {
    flexDirection: 'row',
    position: 'absolute',
    bottom: 10,
    alignSelf: 'center',
  },
  imageIndicator: {
    height: 8,
    borderRadius: 4,
    backgroundColor: 'white',
    marginHorizontal: 4,
  },
  cardLowerSection: {
    flex: 0.3, // Take 30% of the card height for details
    justifyContent: 'flex-end',
    overflow: 'hidden', // Ensure gradient is clipped to card shape
    marginTop: -20, // Overlap slightly with image section
  },
  lowerSectionGradient: {
    flex: 1,
    padding: 20,
    justifyContent: 'flex-end',
    borderBottomLeftRadius: 20,
    borderBottomRightRadius: 20,
  },
  cardContentContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-end',
    width: '100%',
  },
  cardMainContent: {
    flex: 1,
    marginRight: 10,
  },
  nameAgeContainer: {
    flexDirection: 'row',
    alignItems: 'baseline',
  },
  nameText: {
    fontSize: 28,
    fontWeight: 'bold',
    color: Colors.dark.primary, // This will now work after Colors.ts update
  },
  ageText: {
    fontSize: 22,
    color: Colors.dark.text,
    marginLeft: 8,
  },
  bioText: {
    fontSize: 16,
    color: Colors.dark.text,
    marginTop: 5,
  },
  interestContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    marginTop: 10,
  },
  interestPill: {
    backgroundColor: Colors.dark.card,
    borderRadius: 15,
    paddingVertical: 5,
    paddingHorizontal: 10,
    marginRight: 8,
    marginBottom: 8,
    borderColor: Colors.dark.tint,
    borderWidth: 1,
  },
  interestText: {
    color: Colors.dark.tint,
    fontSize: 14,
  },
  sideButtonsContainer: {
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'space-around', // Distribute space between buttons
    height: '100%', // Take full height of lower section
  },
  sideActionButton: {
    backgroundColor: Colors.dark.card,
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 10, // Space between buttons
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 2,
  },
  navigationSection: {
    height: NAV_HEIGHT,
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    backgroundColor: Colors.dark.background,
    paddingBottom: Platform.OS === 'ios' ? 20 : 0, // Padding for iPhone X and later
  },
  navButton: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  noProfilesContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  noProfilesText: {
    color: Colors.dark.text,
    marginTop: 20,
    textAlign: 'center',
  },
  noProfilesSubText: {
    color: Colors.dark.textSecondary, // This will now work after Colors.ts update
    marginTop: 10,
    textAlign: 'center',
    fontSize: 16,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: Colors.dark.text,
    fontSize: 18,
    marginBottom: 20,
  },
  loadingGif: {
    width: 100,
    height: 100,
    resizeMode: 'contain',
  },
  // Added style for button text if you use children prop for Button
  buttonText: {
    color: Colors.dark.text, // Or a specific color like 'white' or Colors.dark.tint
    fontSize: 18,
    fontWeight: 'bold',
  }
});